param([string]$Command, [string]$Version)

function Show-Current {
    Write-Host "Current Node.js version:" -ForegroundColor Green
    try {
        node --version
        npm --version
    } catch {
        Write-Host "Node.js not found in PATH" -ForegroundColor Red
    }
}

function Show-Help {
    Write-Host "Simple Node Version Manager" -ForegroundColor Green
    Write-Host "Commands:"
    Write-Host "  current  - Show current Node.js version"
    Write-Host "  help     - Show this help"
}

switch ($Command) {
    "current" { Show-Current }
    "help" { Show-Help }
    default { Show-Help }
}
