# Manual npm installation for Node.js 12.20.0

Write-Host "Installing npm manually for Node.js 12.20.0..." -ForegroundColor Green

# Get the current Node.js path
$nodeVersion = node --version
Write-Host "Current Node.js version: $nodeVersion" -ForegroundColor Yellow

# Find the Node.js installation directory
$nvmRoot = "d:\nvm"
$nodeDir = "$nvmRoot\v12.20.0"

if (!(Test-Path $nodeDir)) {
    Write-Host "Node.js 12.20.0 directory not found at $nodeDir" -ForegroundColor Red
    exit 1
}

Write-Host "Node.js directory: $nodeDir" -ForegroundColor Yellow

# Check if npm tarball exists
$npmTarball = "$env:TEMP\npm-6.14.8.tgz"
if (!(Test-Path $npmTarball)) {
    Write-Host "npm tarball not found. Downloading..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri "https://registry.npmjs.org/npm/-/npm-6.14.8.tgz" -OutFile $npmTarball
        Write-Host "Downloaded npm tarball" -ForegroundColor Green
    } catch {
        Write-Host "Failed to download npm: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Extract npm
$extractDir = "$env:TEMP\npm-extract"
if (Test-Path $extractDir) {
    Remove-Item $extractDir -Recurse -Force
}
New-Item -ItemType Directory -Path $extractDir -Force | Out-Null

Write-Host "Extracting npm..." -ForegroundColor Yellow

# Use tar to extract (available in Windows 10+)
try {
    tar -xzf $npmTarball -C $extractDir
    Write-Host "Extracted npm tarball" -ForegroundColor Green
} catch {
    Write-Host "Failed to extract npm tarball" -ForegroundColor Red
    exit 1
}

# Find the extracted npm directory
$npmExtracted = Get-ChildItem $extractDir -Directory | Select-Object -First 1
if (!$npmExtracted) {
    Write-Host "Could not find extracted npm directory" -ForegroundColor Red
    exit 1
}

$npmSourceDir = $npmExtracted.FullName
Write-Host "npm source directory: $npmSourceDir" -ForegroundColor Yellow

# Copy npm to node_modules
$nodeModulesDir = "$nodeDir\node_modules"
$npmTargetDir = "$nodeModulesDir\npm"

if (!(Test-Path $nodeModulesDir)) {
    New-Item -ItemType Directory -Path $nodeModulesDir -Force | Out-Null
}

if (Test-Path $npmTargetDir) {
    Remove-Item $npmTargetDir -Recurse -Force
}

Write-Host "Copying npm to $npmTargetDir..." -ForegroundColor Yellow
Copy-Item -Path $npmSourceDir -Destination $npmTargetDir -Recurse -Force

# Create npm.cmd and npx.cmd
$npmCmd = @"
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\node_modules\npm\bin\npm-cli.js" %*
) ELSE (
  @SETLOCAL
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\node_modules\npm\bin\npm-cli.js" %*
)
"@

$npxCmd = @"
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\node_modules\npm\bin\npx-cli.js" %*
) ELSE (
  @SETLOCAL
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\node_modules\npm\bin\npx-cli.js" %*
)
"@

$npmCmd | Out-File -FilePath "$nodeDir\npm.cmd" -Encoding ASCII
$npxCmd | Out-File -FilePath "$nodeDir\npx.cmd" -Encoding ASCII

Write-Host "Created npm.cmd and npx.cmd" -ForegroundColor Green

# Clean up
Remove-Item $extractDir -Recurse -Force
Remove-Item $npmTarball -Force

Write-Host "npm installation completed!" -ForegroundColor Green
Write-Host "Please restart your terminal and run 'npm --version' to verify." -ForegroundColor Yellow
