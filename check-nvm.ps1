# Check nvm installation status

Write-Host "Checking nvm installation..." -ForegroundColor Green

# Check if nvm command is available
try {
    $nvmVersion = nvm version
    Write-Host "✅ nvm is installed! Version: $nvmVersion" -ForegroundColor Green
    
    # Show nvm commands
    Write-Host "`nAvailable nvm commands:" -ForegroundColor Cyan
    nvm
    
} catch {
    Write-Host "❌ nvm is not installed or not in PATH" -ForegroundColor Red
    
    # Check common installation locations
    $commonPaths = @(
        "$env:ProgramFiles\nodejs\nvm.exe",
        "$env:ProgramFiles(x86)\nodejs\nvm.exe", 
        "$env:APPDATA\nvm\nvm.exe",
        "$env:USERPROFILE\.nvm\nvm.exe",
        "C:\nvm\nvm.exe"
    )
    
    Write-Host "`nChecking common installation locations:" -ForegroundColor Yellow
    foreach ($path in $commonPaths) {
        if (Test-Path $path) {
            Write-Host "✅ Found nvm at: $path" -ForegroundColor Green
            Write-Host "You may need to add this to your PATH" -ForegroundColor Yellow
        } else {
            Write-Host "❌ Not found: $path" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nTo install nvm-windows:" -ForegroundColor Cyan
    Write-Host "1. Run PowerShell as Administrator" -ForegroundColor White
    Write-Host "2. Run: winget install CoreyButler.NVMforWindows" -ForegroundColor White
    Write-Host "3. Or download from: https://github.com/coreybutler/nvm-windows/releases" -ForegroundColor White
}

# Check current Node.js
Write-Host "`nCurrent Node.js status:" -ForegroundColor Cyan
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js not found" -ForegroundColor Red
}
