@echo off
setlocal enabledelayedexpansion

:: Simple Node Version Manager for Windows
:: Usage: nvm-simple.bat [command] [version]

set "NVM_HOME=%USERPROFILE%\.nvm-simple"
set "NODE_VERSIONS_DIR=%NVM_HOME%\versions"

if not exist "%NVM_HOME%" mkdir "%NVM_HOME%"
if not exist "%NODE_VERSIONS_DIR%" mkdir "%NODE_VERSIONS_DIR%"

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="list" goto :list
if "%1"=="install" goto :install
if "%1"=="use" goto :use
if "%1"=="current" goto :current

:help
echo Simple Node Version Manager
echo.
echo Commands:
echo   nvm-simple list          - List installed Node.js versions
echo   nvm-simple install ^<version^> - Install a Node.js version
echo   nvm-simple use ^<version^>     - Switch to a Node.js version
echo   nvm-simple current       - Show current Node.js version
echo.
goto :end

:list
echo Installed Node.js versions:
if exist "%NODE_VERSIONS_DIR%" (
    for /d %%i in ("%NODE_VERSIONS_DIR%\*") do (
        echo   %%~ni
    )
) else (
    echo   No versions installed
)
goto :end

:current
node --version 2>nul
if errorlevel 1 (
    echo No Node.js version currently active
) else (
    echo Current Node.js version: 
    node --version
)
goto :end

:install
if "%2"=="" (
    echo Please specify a version to install
    echo Example: nvm-simple install 18.17.0
    goto :end
)

set "VERSION=%2"
set "VERSION_DIR=%NODE_VERSIONS_DIR%\%VERSION%"

if exist "%VERSION_DIR%" (
    echo Node.js %VERSION% is already installed
    goto :end
)

echo Installing Node.js %VERSION%...
echo.
echo Please manually download Node.js %VERSION% from:
echo https://nodejs.org/dist/v%VERSION%/node-v%VERSION%-win-x64.zip
echo.
echo Extract it to: %VERSION_DIR%
echo.
echo After extraction, run: nvm-simple use %VERSION%
goto :end

:use
if "%2"=="" (
    echo Please specify a version to use
    echo Example: nvm-simple use 18.17.0
    goto :end
)

set "VERSION=%2"
set "VERSION_DIR=%NODE_VERSIONS_DIR%\%VERSION%"

if not exist "%VERSION_DIR%" (
    echo Node.js %VERSION% is not installed
    echo Run: nvm-simple install %VERSION%
    goto :end
)

:: Remove current Node.js from PATH
set "NEW_PATH="
for %%i in ("%PATH:;=" "%") do (
    set "SEGMENT=%%~i"
    echo !SEGMENT! | findstr /i "nodejs" >nul
    if errorlevel 1 (
        if "!NEW_PATH!"=="" (
            set "NEW_PATH=!SEGMENT!"
        ) else (
            set "NEW_PATH=!NEW_PATH!;!SEGMENT!"
        )
    )
)

:: Add new Node.js version to PATH
set "NEW_PATH=%VERSION_DIR%;!NEW_PATH!"

:: Update PATH for current session
set "PATH=!NEW_PATH!"

echo Switched to Node.js %VERSION%
node --version

:: Create a batch file to set PATH permanently
echo @echo off > "%NVM_HOME%\set-path.bat"
echo setx PATH "!NEW_PATH!" >> "%NVM_HOME%\set-path.bat"
echo echo PATH updated permanently >> "%NVM_HOME%\set-path.bat"

echo.
echo To make this change permanent, run:
echo %NVM_HOME%\set-path.bat
goto :end

:end
