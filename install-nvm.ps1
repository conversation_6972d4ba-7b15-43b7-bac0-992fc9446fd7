# PowerShell script to manually install nvm-windows

$nvmDir = "$env:USERPROFILE\.nvm"
$nvmExe = "$nvmDir\nvm.exe"

Write-Host "Setting up nvm-windows manually..." -ForegroundColor Green

# Create nvm directory if it doesn't exist
if (!(Test-Path $nvmDir)) {
    New-Item -ItemType Directory -Path $nvmDir -Force
    Write-Host "Created directory: $nvmDir" -ForegroundColor Yellow
}

# Check if we can download nvm.exe directly
try {
    Write-Host "Attempting to download nvm.exe..." -ForegroundColor Yellow
    $url = "https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm.exe"
    Invoke-WebRequest -Uri $url -OutFile $nvmExe -ErrorAction Stop
    Write-Host "Downloaded nvm.exe successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to download nvm.exe: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please manually download from: https://github.com/coreybutler/nvm-windows/releases" -ForegroundColor Yellow
    return
}

# Add nvm to PATH for current session
$env:PATH = "$nvmDir;$env:PATH"

# Add nvm to PATH permanently
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$nvmDir*") {
    [Environment]::SetEnvironmentVariable("PATH", "$nvmDir;$currentPath", "User")
    Write-Host "Added nvm to PATH permanently" -ForegroundColor Green
}

# Create settings.txt for nvm
$settingsContent = @"
root: $nvmDir\nodejs
path: $nvmDir\nodejs
arch: 64
proxy: none
"@

$settingsFile = "$nvmDir\settings.txt"
$settingsContent | Out-File -FilePath $settingsFile -Encoding ASCII
Write-Host "Created settings file: $settingsFile" -ForegroundColor Green

# Test nvm installation
Write-Host "Testing nvm installation..." -ForegroundColor Yellow
try {
    & $nvmExe version
    Write-Host "nvm installed successfully!" -ForegroundColor Green
    Write-Host "Please restart your terminal and run 'nvm version' to verify." -ForegroundColor Yellow
} catch {
    Write-Host "nvm installation may have issues. Please check manually." -ForegroundColor Red
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Restart your terminal" -ForegroundColor White
Write-Host "2. Run 'nvm version' to verify installation" -ForegroundColor White
Write-Host "3. Run 'nvm list available' to see available Node.js versions" -ForegroundColor White
Write-Host "4. Run 'nvm install 18.17.0' to install Node.js 18.17.0" -ForegroundColor White
Write-Host "5. Run 'nvm use 18.17.0' to switch to that version" -ForegroundColor White
