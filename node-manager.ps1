# Simple Node.js Version Manager
param(
    [string]$Command,
    [string]$Version
)

$nodeDir = "$env:USERPROFILE\.node-versions"
$currentNodeLink = "$env:USERPROFILE\.current-node"

function Show-Help {
    Write-Host "Node.js Version Manager" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\node-manager.ps1 list                    - List installed versions"
    Write-Host "  .\node-manager.ps1 install VERSION         - Install specified version"
    Write-Host "  .\node-manager.ps1 use VERSION             - Switch to specified version"
    Write-Host "  .\node-manager.ps1 current                 - Show current version"
    Write-Host "  .\node-manager.ps1 available               - Show available versions"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\node-manager.ps1 install 18.17.0"
    Write-Host "  .\node-manager.ps1 use 18.17.0"
}

function Get-AvailableVersions {
    Write-Host "Getting available Node.js versions..." -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri "https://npmmirror.com/mirrors/node/" -TimeoutSec 30
        $versions = $response | Where-Object { $_.name -match "^v\d+\.\d+\.\d+/$" } |
                   ForEach-Object { $_.name.TrimEnd('/') } |
                   Sort-Object { [Version]($_ -replace '^v', '') } -Descending |
                   Select-Object -First 20

        Write-Host "Latest 20 versions:" -ForegroundColor Green
        $versions | ForEach-Object { Write-Host "  $_" }
    } catch {
        Write-Host "Could not get version list: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please visit https://nodejs.org/en/download/releases/ to see available versions" -ForegroundColor Yellow
    }
}

function Install-NodeVersion {
    param([string]$ver)

    if (!$ver) {
        Write-Host "Please specify version to install, e.g.: 18.17.0" -ForegroundColor Red
        return
    }

    if ($ver -notmatch '^\d+\.\d+\.\d+$') {
        Write-Host "Invalid version format, use: 18.17.0" -ForegroundColor Red
        return
    }

    $versionDir = "$nodeDir\v$ver"
    if (Test-Path $versionDir) {
        Write-Host "Node.js v$ver is already installed" -ForegroundColor Yellow
        return
    }

    Write-Host "Installing Node.js v$ver..." -ForegroundColor Green
    
    # 创建版本目录
    New-Item -ItemType Directory -Path $versionDir -Force | Out-Null
    
    # 下载 Node.js
    $arch = if ([Environment]::Is64BitOperatingSystem) { "x64" } else { "x86" }
    $downloadUrl = "https://npmmirror.com/mirrors/node/v$ver/node-v$ver-win-$arch.zip"
    $zipFile = "$env:TEMP\node-v$ver-win-$arch.zip"
    
    try {
        Write-Host "下载中: $downloadUrl" -ForegroundColor Yellow
        Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -TimeoutSec 300
        
        Write-Host "解压中..." -ForegroundColor Yellow
        Expand-Archive -Path $zipFile -DestinationPath $env:TEMP -Force
        
        $extractedDir = "$env:TEMP\node-v$ver-win-$arch"
        Copy-Item -Path "$extractedDir\*" -Destination $versionDir -Recurse -Force
        
        # 清理
        Remove-Item $zipFile -Force
        Remove-Item $extractedDir -Recurse -Force
        
        Write-Host "Node.js v$ver 安装成功!" -ForegroundColor Green
        Write-Host "运行 '.\node-manager.ps1 use $ver' 来切换到这个版本" -ForegroundColor Yellow
        
    } catch {
        Write-Host "安装失败: $($_.Exception.Message)" -ForegroundColor Red
        if (Test-Path $versionDir) {
            Remove-Item $versionDir -Recurse -Force
        }
    }
}

function Use-NodeVersion {
    param([string]$ver)
    
    if (!$ver) {
        Write-Host "请指定要使用的版本" -ForegroundColor Red
        return
    }
    
    $versionDir = "$nodeDir\v$ver"
    if (!(Test-Path $versionDir)) {
        Write-Host "Node.js v$ver 未安装，请先运行: .\node-manager.ps1 install $ver" -ForegroundColor Red
        return
    }
    
    # 更新 PATH
    $newPath = $env:PATH -split ';' | Where-Object { $_ -notlike "*\.node-versions*" -and $_ -notlike "*nodejs*" }
    $newPath = @($versionDir) + $newPath
    $env:PATH = $newPath -join ';'
    
    # 保存当前版本
    $ver | Out-File -FilePath $currentNodeLink -Encoding UTF8
    
    Write-Host "已切换到 Node.js v$ver" -ForegroundColor Green
    & "$versionDir\node.exe" --version
    & "$versionDir\npm.cmd" --version
    
    Write-Host ""
    Write-Host "要永久应用此更改，请将以下路径添加到系统 PATH 的最前面:" -ForegroundColor Yellow
    Write-Host $versionDir -ForegroundColor Cyan
}

function Get-InstalledVersions {
    if (!(Test-Path $nodeDir)) {
        Write-Host "没有安装任何版本" -ForegroundColor Yellow
        return
    }
    
    $versions = Get-ChildItem $nodeDir -Directory | ForEach-Object { $_.Name }
    if ($versions.Count -eq 0) {
        Write-Host "没有安装任何版本" -ForegroundColor Yellow
    } else {
        Write-Host "已安装的版本:" -ForegroundColor Green
        $versions | ForEach-Object { Write-Host "  $_" }
    }
}

function Get-CurrentVersion {
    if (Test-Path $currentNodeLink) {
        $current = Get-Content $currentNodeLink
        Write-Host "当前版本: v$current" -ForegroundColor Green
    }
    
    try {
        $nodeVersion = node --version
        $npmVersion = npm --version
        Write-Host "Node.js: $nodeVersion" -ForegroundColor Cyan
        Write-Host "npm: $npmVersion" -ForegroundColor Cyan
    } catch {
        Write-Host "Node.js 未在 PATH 中找到" -ForegroundColor Red
    }
}

# 创建版本目录
if (!(Test-Path $nodeDir)) {
    New-Item -ItemType Directory -Path $nodeDir -Force | Out-Null
}

# 处理命令
switch ($Command.ToLower()) {
    "list" { Get-InstalledVersions }
    "install" { Install-NodeVersion $Version }
    "use" { Use-NodeVersion $Version }
    "current" { Get-CurrentVersion }
    "available" { Get-AvailableVersions }
    default { Show-Help }
}
