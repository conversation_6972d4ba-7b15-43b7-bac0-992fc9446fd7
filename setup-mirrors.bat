@echo off
echo 配置国内镜像源...
echo.

echo 1. 配置 npm 镜像源...
npm config set registry https://registry.npmmirror.com
npm config set disturl https://npmmirror.com/dist
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver/
npm config set operadriver_cdnurl https://npmmirror.com/mirrors/operadriver/
npm config set fse_binary_host_mirror https://npmmirror.com/mirrors/fsevents/

echo 2. 配置 yarn 镜像源...
yarn config set registry https://registry.npmmirror.com 2>nul
yarn config set disturl https://npmmirror.com/dist 2>nul
yarn config set electron_mirror https://npmmirror.com/mirrors/electron/ 2>nul
yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass/ 2>nul
yarn config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/ 2>nul
yarn config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver/ 2>nul
yarn config set operadriver_cdnurl https://npmmirror.com/mirrors/operadriver/ 2>nul
yarn config set fse_binary_host_mirror https://npmmirror.com/mirrors/fsevents/ 2>nul

echo 3. 配置 pnpm 镜像源...
pnpm config set registry https://registry.npmmirror.com 2>nul

echo 4. 配置环境变量...
setx ELECTRON_MIRROR "https://npmmirror.com/mirrors/electron/"
setx SASS_BINARY_SITE "https://npmmirror.com/mirrors/node-sass/"
setx PHANTOMJS_CDNURL "https://npmmirror.com/mirrors/phantomjs/"
setx CHROMEDRIVER_CDNURL "https://npmmirror.com/mirrors/chromedriver/"
setx OPERADRIVER_CDNURL "https://npmmirror.com/mirrors/operadriver/"

echo.
echo 镜像源配置完成！
echo.
echo 当前 npm 配置:
npm config get registry
echo.
echo 测试连接...
npm ping
echo.
echo 如果需要恢复官方源，请运行:
echo npm config set registry https://registry.npmjs.org/
echo.
pause
