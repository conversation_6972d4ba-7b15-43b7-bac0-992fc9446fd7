# 使用国内镜像安装 nvm-windows

$nvmDir = "$env:USERPROFILE\.nvm"
$nvmExe = "$nvmDir\nvm.exe"

Write-Host "使用国内镜像安装 nvm-windows..." -ForegroundColor Green

# 创建 nvm 目录
if (!(Test-Path $nvmDir)) {
    New-Item -ItemType Directory -Path $nvmDir -Force
    Write-Host "创建目录: $nvmDir" -ForegroundColor Yellow
}

# 尝试从不同的镜像源下载
$mirrors = @(
    "https://ghproxy.com/https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm.exe",
    "https://mirror.ghproxy.com/https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm.exe",
    "https://gh.api.99988866.xyz/https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm.exe"
)

$downloaded = $false
foreach ($mirror in $mirrors) {
    try {
        Write-Host "尝试从镜像下载: $mirror" -ForegroundColor Yellow
        Invoke-WebRequest -Uri $mirror -OutFile $nvmExe -TimeoutSec 30 -ErrorAction Stop
        Write-Host "下载成功!" -ForegroundColor Green
        $downloaded = $true
        break
    } catch {
        Write-Host "下载失败: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

if (!$downloaded) {
    Write-Host "所有镜像都无法下载，请手动下载 nvm.exe" -ForegroundColor Red
    Write-Host "下载地址: https://github.com/coreybutler/nvm-windows/releases/download/1.2.2/nvm.exe" -ForegroundColor Yellow
    Write-Host "保存到: $nvmExe" -ForegroundColor Yellow
    return
}

# 添加到 PATH
$env:PATH = "$nvmDir;$env:PATH"

# 永久添加到 PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$nvmDir*") {
    [Environment]::SetEnvironmentVariable("PATH", "$nvmDir;$currentPath", "User")
    Write-Host "已添加到 PATH" -ForegroundColor Green
}

# 创建配置文件
$settingsContent = @"
root: $nvmDir\nodejs
path: $nvmDir\nodejs
arch: 64
proxy: none
node_mirror: https://npmmirror.com/mirrors/node/
npm_mirror: https://npmmirror.com/mirrors/npm/
"@

$settingsFile = "$nvmDir\settings.txt"
$settingsContent | Out-File -FilePath $settingsFile -Encoding ASCII
Write-Host "创建配置文件: $settingsFile" -ForegroundColor Green

# 测试安装
Write-Host "测试 nvm 安装..." -ForegroundColor Yellow
try {
    $version = & $nvmExe version
    Write-Host "nvm 安装成功! 版本: $version" -ForegroundColor Green
} catch {
    Write-Host "nvm 可能有问题，请手动检查" -ForegroundColor Red
}

Write-Host "`n下一步操作:" -ForegroundColor Cyan
Write-Host "1. 重启终端" -ForegroundColor White
Write-Host "2. 运行 'nvm version' 验证安装" -ForegroundColor White
Write-Host "3. 运行 'nvm list available' 查看可用版本" -ForegroundColor White
Write-Host "4. 运行 'nvm install 18.17.0' 安装 Node.js" -ForegroundColor White
Write-Host "5. 运行 'nvm use 18.17.0' 切换版本" -ForegroundColor White
